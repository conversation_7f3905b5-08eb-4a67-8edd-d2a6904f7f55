#include <X11/Xlib.h>
#include <X11/Xutil.h>
#include <libdrm/drm.h>
#include <libdrm/drm_mode.h>
#include <xf86drm.h>
#include <xf86drmMode.h>
#include <fcntl.h>
#include <sys/mman.h>
#include <sys/poll.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>

static void handle_page_flip(int fd, unsigned int sequence, unsigned int tv_sec, unsigned int tv_usec, void *user_data)
{
  // 页面翻转事件处理
  (void)fd; (void)sequence; (void)tv_sec; (void)tv_usec; (void)user_data;
  // 空函数，仅清理事件
}

int main() {
    // 打开 X11 显示
    Display *display = XOpenDisplay(NULL);
    if (!display) {
        fprintf(stderr, "无法打开 X 显示\n");
        return 1;
    }
    Window root = DefaultRootWindow(display);
    XWindowAttributes attrs;
    XGetWindowAttributes(display, root, &attrs);
    int WIDTH = attrs.width;
    int HEIGHT = attrs.height;

    printf("捕获分辨率: %dx%d\n", WIDTH, HEIGHT);

    // 打开 VirtIO-GPU 的 DRM 设备
    int drm_fd = open("/dev/dri/card0", O_RDWR | O_CLOEXEC);
    if (drm_fd < 0) {
        perror("无法打开 /dev/dri/card0");
        XCloseDisplay(display);
        return 1;
    }

    // 获取 DRM 资源
    drmModeRes *res = drmModeGetResources(drm_fd);
    if (!res) {
        perror("无法获取 DRM 资源");
        close(drm_fd);
        XCloseDisplay(display);
        return 1;
    }

    // 打印 CRTC 和连接器信息
    printf("可用 CRTCs: %d\n", res->count_crtcs);
    for (int i = 0; i < res->count_crtcs; i++) {
        printf("CRTC ID: %u\n", res->crtcs[i]);
    }
    printf("可用连接器: %d\n", res->count_connectors);
    for (int i = 0; i < res->count_connectors; i++) {
        printf("连接器 ID: %u\n", res->connectors[i]);
    }

    // 查找已连接的连接器
    drmModeConnector *conn = NULL;
    for (int i = 0; i < res->count_connectors; i++) {
        conn = drmModeGetConnector(drm_fd, res->connectors[i]);
        if (conn->connection == DRM_MODE_CONNECTED && conn->count_modes > 0) {
            printf("找到连接器: ID=%u, 模式数=%d\n", conn->connector_id, conn->count_modes);
            break;
        }
        drmModeFreeConnector(conn);
        conn = NULL;
    }
    if (!conn) {
        perror("无法找到已连接的连接器");
        drmModeFreeResources(res);
        close(drm_fd);
        XCloseDisplay(display);
        return 1;
    }

    // 打印支持的模式
    for (int i = 0; i < conn->count_modes; i++) {
        printf("模式 %d: %dx%d@%.2fHz\n", i, conn->modes[i].hdisplay, conn->modes[i].vdisplay,
               (float)conn->modes[i].vrefresh);
    }

    // 验证分辨率
    int mode_found = 0;
    int mode_index = 0;
    for (int i = 0; i < conn->count_modes; i++) {
        if (conn->modes[i].hdisplay == WIDTH && conn->modes[i].vdisplay == HEIGHT) {
            mode_found = 1;
            mode_index = i;
            break;
        }
    }
    if (!mode_found) {
        fprintf(stderr, "VirtIO-GPU 不支持分辨率 %dx%d，尝试 1280x800\n", WIDTH, HEIGHT);
        WIDTH = 1280;
        HEIGHT = 800;
        for (int i = 0; i < conn->count_modes; i++) {
            if (conn->modes[i].hdisplay == WIDTH && conn->modes[i].vdisplay == HEIGHT) {
                mode_found = 1;
                mode_index = i;
                break;
            }
        }
        if (!mode_found) {
            fprintf(stderr, "VirtIO-GPU 不支持 1280x800\n");
            drmModeFreeConnector(conn);
            drmModeFreeResources(res);
            close(drm_fd);
            XCloseDisplay(display);
            return 1;
        }
    }

    // 创建哑缓冲区
    struct drm_mode_create_dumb create = {
        .width = WIDTH,
        .height = HEIGHT,
        .bpp = 32,
    };
    if (drmIoctl(drm_fd, DRM_IOCTL_MODE_CREATE_DUMB, &create) < 0) {
        perror("无法创建哑缓冲区");
        drmModeFreeConnector(conn);
        drmModeFreeResources(res);
        close(drm_fd);
        XCloseDisplay(display);
        return 1;
    }

    // 映射哑缓冲区
    struct drm_mode_map_dumb map = { .handle = create.handle };
    if (drmIoctl(drm_fd, DRM_IOCTL_MODE_MAP_DUMB, &map) < 0) {
        perror("无法映射哑缓冲区");
        drmModeFreeConnector(conn);
        drmModeFreeResources(res);
        close(drm_fd);
        XCloseDisplay(display);
        return 1;
    }

    void *fb_data = mmap(NULL, create.size, PROT_READ | PROT_WRITE, MAP_SHARED, drm_fd, map.offset);
    if (fb_data == MAP_FAILED) {
        perror("mmap 失败");
        drmModeFreeConnector(conn);
        drmModeFreeResources(res);
        close(drm_fd);
        XCloseDisplay(display);
        return 1;
    }

    // 创建帧缓冲区
    uint32_t fb_id;
    if (drmModeAddFB(drm_fd, WIDTH, HEIGHT, 24, 32, create.pitch, create.handle, &fb_id) < 0) {
        perror("无法创建帧缓冲区");
        munmap(fb_data, create.size);
        drmModeFreeConnector(conn);
        drmModeFreeResources(res);
        close(drm_fd);
        XCloseDisplay(display);
        return 1;
    }

    // 设置 CRTC
    uint32_t crtc_id = res->crtcs[0];
    printf("设置 CRTC: crtc_id=%u, fb_id=%u, connector_id=%u, 分辨率=%dx%d\n",
           crtc_id, fb_id, conn->connector_id, WIDTH, HEIGHT);
    if (drmModeSetCrtc(drm_fd, crtc_id, fb_id, 0, 0, &conn->connector_id, 1, &conn->modes[mode_index]) < 0) {
        perror("无法设置 CRTC");
        printf("错误码: %d\n", errno);
        drmModeRmFB(drm_fd, fb_id);
        munmap(fb_data, create.size);
        drmModeFreeConnector(conn);
        drmModeFreeResources(res);
        close(drm_fd);
        XCloseDisplay(display);
        return 1;
    }

    // 验证 CRTC 绑定
    drmModeCrtc *crtc = drmModeGetCrtc(drm_fd, crtc_id);
    if (crtc) {
        printf("CRTC 状态: fb_id=%u, mode_valid=%d, 分辨率=%dx%d, size: %lld\n",
               crtc->buffer_id, crtc->mode_valid, crtc->width, crtc->height, create.size);
        drmModeFreeCrtc(crtc);
    }
    // 设置 DRM 事件上下文
    drmEventContext evctx = {
        .version = DRM_EVENT_CONTEXT_VERSION,
       .page_flip_handler = handle_page_flip,
    };

    // 测试纯色填充
    memset(fb_data, 0, create.size);
    for (size_t i = 0; i < create.size; i += 4) {
        ((char *)fb_data)[i + 0] = 255;   // B
        ((char *)fb_data)[i + 1] = 0;   // G
        ((char *)fb_data)[i + 2] = 0;   //R
        ((char *)fb_data)[i + 3] = 255; // A
    }

    if (drmModePageFlip(drm_fd, crtc_id, fb_id, DRM_MODE_PAGE_FLIP_EVENT, NULL) < 0) {
       perror("页面翻转失败");
    }

    // 处理初始翻转事件
    struct pollfd pfd = { .fd = drm_fd, .events = POLLIN };
    poll(&pfd, 1, -1);
    drmHandleEvent(drm_fd, &evctx);

    sleep(20); // 观察红色画面

    // 主循环：捕获物理显卡的桌面
    while (1) {
        XImage *image = XGetImage(display, root, 0, 0, WIDTH, HEIGHT, AllPlanes, ZPixmap);
        if (!image) {
            fprintf(stderr, "无法捕获 X 图像\n");
            break;
        }
        printf("XImage 格式: depth=%d, bits_per_pixel=%d\n", image->depth, image->bits_per_pixel);
        memcpy(fb_data, image->data, create.size);
        if (drmModePageFlip(drm_fd, crtc_id, fb_id, DRM_MODE_PAGE_FLIP_EVENT, NULL) < 0) {
           perror("页面翻转失败");
        } else {
	    // 等待页面翻转完成
	    poll(&pfd, 1, -1);
	    drmHandleEvent(drm_fd, &evctx);
	}
        XDestroyImage(image);
        usleep(30000); // 约 60 FPS
    }

    // 清理
    drmModeRmFB(drm_fd, fb_id);
    munmap(fb_data, create.size);
    drmModeFreeConnector(conn);
    drmModeFreeResources(res);
    close(drm_fd);
    XCloseDisplay(display);
    return 0;
}
